import streamlit as st
import math

st.set_page_config(layout="wide") # لجعل الواجهة أعرض

st.title("📄 برنامج فواتير بتصميم تفاعلي")
st.write("---")

# --- قسم الحساب بالدولار ---
st.subheader("الحساب بالدولار الأمريكي ($)")

col1, col2, col3 = st.columns(3)

with col1:
    gold_weight = st.number_input("⚖️ وزن الذهب (جرام)", min_value=0.0, format="%.2f", key="gold")

with col2:
    workmanship_price_usd = st.number_input("💲 سعر مصنعية الجرام", min_value=0.0, format="%.2f", key="price_usd")

# حساب ناتج المصنعية تلقائياً
subtotal_usd = gold_weight * workmanship_price_usd

with col3:
    # عرض الناتج في مربع خاص مثل Excel
    st.metric(label=" الناتج (قبل التقريب)", value=f"$ {subtotal_usd:.2f}")


# خيار التقريب
round_usd_checkbox = st.checkbox("تقريب الناتج لأعلى رقم صحيح؟")
final_usd_charge = subtotal_usd
if round_usd_checkbox:
    final_usd_charge = math.ceil(subtotal_usd)
    # عرض الناتج بعد التقريب إذا تم تفعيله
    st.info(f"الناتج بعد التقريب: $ {final_usd_charge:.2f}")

st.write("---")

# --- قسم الحساب بالجنيه المصري ---
st.subheader("الحساب بالجنيه المصري (EGP)")

col_egp1, col_egp2, col_egp3 = st.columns(3)

with col_egp1:
    total_stone_setting_egp = st.number_input(" تركيب الأحجار", min_value=0.0, format="%.2f", key="stone_egp")

with col_egp2:
    other_services_egp = st.number_input(" خدمات أخرى (بانيو/دمغة)", min_value=0.0, format="%.2f", key="other_egp")

# حساب المجموع تلقائياً
final_egp_charge = total_stone_setting_egp + other_services_egp

with col_egp3:
    st.metric(label=" الناتج بالجنيه", value=f"{final_egp_charge:.2f} جنيه")

st.write("---")

# --- زر عرض الملخص النهائي ---
st.header("الملخص النهائي")
customer_name = st.text_input("اسم العميل:", key="customer")

if st.button("احفظ الفاتورة وأظهر الملخص"):
    if not customer_name:
        st.error("خطأ: الرجاء إدخال اسم العميل.")
    else:
        st.success(f"""
        ### تم تسجيل الفاتورة للعميل: {customer_name}
        - **الذهب:** سيتم خصم **{-gold_weight:.2f}** جرام من رصيده.
        - **الدولار:** سيضاف **${final_usd_charge:.2f}** إلى حسابه.
        - **الجنيه:** سيضاف **{final_egp_charge:.2f}** جنيه إلى حسابه.
        """)