import streamlit as st
import math

st.set_page_config(layout="wide") # استخدام كامل عرض الشاشة

st.title("📄 نظام الفواتير المفصل للورشة")
st.write("---")

# --- حاوية المعلومات الأساسية ---
with st.container(border=True):
    st.subheader("معلومات الفاتورة الأساسية")
    col1, col2 = st.columns(2)
    with col1:
        customer_name = st.text_input("👤 اسم العميل:")
    with col2:
        invoice_date = st.date_input("🗓️ تاريخ الفاتورة")

st.write("---")


# --- حاوية حسابات الذهب والمصنعية (بالدولار) ---
with st.container(border=True):
    st.subheader("⚖️ حساب الذهب والمصنعية ($)")
    col1, col2, col3 = st.columns(3)

    with col1:
        gold_weight = st.number_input("وزن الذهب (جرام)", min_value=0.0, format="%.2f")
    with col2:
        workmanship_price_usd = st.number_input("سعر مصنعية الجرام ($)", min_value=0.0, format="%.2f")
    
    # حساب ناتج المصنعية تلقائياً
    workmanship_subtotal_usd = gold_weight * workmanship_price_usd
    
    with col3:
        st.metric(label="ناتج المصنعية", value=f"$ {workmanship_subtotal_usd:.2f}")

st.write("---")

# --- حاوية حسابات الأحجار ---
with st.container(border=True):
    st.subheader("💎 حساب الأحجار ومواصفاتها")
    
    stone_source = st.radio(
        "اختر مصدر الأحجار:",
        ["الأحجار من العميل (طرفكم)", "الأحجار من الورشة (طرفنا)"],
        horizontal=True,
    )

    stone_cost_usd = 0
    stone_setting_cost_egp = 0

    # --- الحالة الأولى: الأحجار من العميل ---
    if "طرفكم" in stone_source:
        st.info("سيتم حساب تكلفة التركيب بالجنيه المصري.")
        c1, c2, c3 = st.columns(3)
        with c1:
            stone_count = st.number_input("عدد الأحجار", min_value=0, step=1)
        with c2:
            stone_setting_price_egp = st.number_input("سعر تركيب الحجر الواحد (EGP)", min_value=0.0, format="%.2f")
        
        stone_setting_cost_egp = stone_count * stone_setting_price_egp
        with c3:
            st.metric(label="إجمالي تكلفة التركيب", value=f"{stone_setting_cost_egp:.2f} جنيه")

    # --- الحالة الثانية: الأحجار من الورشة ---
    else:
        st.info("سيتم حساب سعر بيع الأحجار بالدولار الأمريكي.")
        c1, c2, c3 = st.columns(3)
        with c1:
            stone_weight_carats = st.number_input("وزن الأحجار (قيراط)", min_value=0.0, format="%.3f")
        with c2:
            stone_price_per_carat_usd = st.number_input("سعر القيراط ($)", min_value=0.0, format="%.2f")

        stone_cost_usd = stone_weight_carats * stone_price_per_carat_usd
        with c3:
            st.metric(label="إجمالي سعر الأحجار", value=f"$ {stone_cost_usd:.2f}")

    # --- مواصفات الأحجار ---
    st.write("---")
    st.write("**مواصفات الأحجار (اختياري):**")
    spec_col1, spec_col2, spec_col3 = st.columns(3)
    with spec_col1:
        st.text_input("مقاس الحجر")
    with spec_col2:
        st.text_input("نوع الحجر")
    with spec_col3:
        st.text_input("اللون والنقاء (Quality)")


st.write("---")


# --- حاوية الخدمات الإضافية والتصليح (بالجنيه) ---
with st.container(border=True):
    st.subheader("🔧 خدمات إضافية وتصليح (EGP)")
    serv_col1, serv_col2, serv_col3, serv_col4 = st.columns(4)
    with serv_col1:
        plating_white_egp = st.number_input("بانيو أبيض", min_value=0.0, format="%.2f")
    with serv_col2:
        plating_yellow_egp = st.number_input("بانيو أصفر", min_value=0.0, format="%.2f")
    with serv_col3:
        hallmark_egp = st.number_input("دمغة", min_value=0.0, format="%.2f")
    with serv_col4:
        repair_egp = st.number_input("تصليح", min_value=0.0, format="%.2f")


st.write("---")


# --- الملخص النهائي ---
with st.container(border=True):
    st.header("💵 الملخص النهائي للحساب")
    
    # --- تجميع الحسابات ---
    final_usd_charge = workmanship_subtotal_usd + stone_cost_usd
    final_egp_charge = stone_setting_cost_egp + plating_white_egp + plating_yellow_egp + hallmark_egp + repair_egp

    # --- خيار التقريب ---
    round_usd_checkbox = st.checkbox("تقريب المبلغ بالدولار لأعلى رقم صحيح؟")
    if round_usd_checkbox:
        final_usd_charge = math.ceil(final_usd_charge)

    # --- عرض النتائج النهائية ---
    res_col1, res_col2, res_col3 = st.columns(3)
    with res_col1:
        st.metric("التغير في رصيد الذهب", f"{-gold_weight:.2f} جرام")
    with res_col2:
        st.metric("إجمالي المطلوب بالدولار", f"$ {final_usd_charge:.2f}")
    with res_col3:
        st.metric("إجمالي المطلوب بالجنيه", f"{final_egp_charge:.2f} جنيه")

    if st.button("حفظ وطباعة الفاتورة"):
        # هنا سنضيف لاحقًا كود الحفظ والطباعة
        st.success(f"تم تسجيل الفاتورة للعميل: {customer_name}")