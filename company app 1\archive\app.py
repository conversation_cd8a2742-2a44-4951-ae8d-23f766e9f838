import streamlit as st
import math
import pandas as pd # استيراد المكتبة الجديدة
import os # لاستخدامها في التعامل مع الملفات

# --- الإعدادات الأولية ---
st.set_page_config(layout="wide", page_title="نظام فواتير الورشة")
st.title("📄 نظام فواتير الورشة")

# --- دالة لحفظ البيانات في ملف CSV ---
def save_invoice_to_csv(customer, date, gold, usd, egp, desc):
    # تحديد الأعمدة التي نريدها في الملف
    columns = ["customer_name", "date", "description", "gold_change", "usd_change", "egp_change"]
    # إنشاء صف جديد بالبيانات
    new_data = pd.DataFrame([[customer, date, desc, gold, usd, egp]], columns=columns)
    
    # التحقق إذا كان الملف موجودًا أم لا
    if not os.path.isfile('invoices.csv'):
        # إذا لم يكن موجودًا، قم بإنشائه مع العناوين
        new_data.to_csv('invoices.csv', index=False, encoding='utf-8-sig')
    else:
        # إذا كان موجودًا، قم بإضافة البيانات بدون العناوين
        new_data.to_csv('invoices.csv', mode='a', header=False, index=False, encoding='utf-8-sig')

# ... (باقي الكود الخاص بالواجهة يبقى كما هو) ...

# --- حاوية المعلومات الأساسية ---
with st.container(border=True):
    col1, col2 = st.columns([3, 1])
    with col1:
        customer_name = st.text_input("👤 اسم العميل:")
    with col2:
        invoice_date = st.date_input("🗓️ تاريخ الفاتورة")
    
    invoice_description = st.text_input("📝 بيان الفاتورة (وصف مختصر):", placeholder="مثال: حلق موديل 123")


# ... (كل حاويات الحسابات تبقى كما هي بدون تغيير) ...
# (حساب الذهب والمصنعية، حساب الأحجار، الخدمات، المواصفات)
# --- The calculation containers from the previous code go here ---
# To keep this brief, I'm omitting the identical parts. 
# Please make sure you have the calculation containers in your file.
# The following is just a placeholder to indicate where the previous UI code should be.

with st.container(border=True):
    st.subheader("⚖️ حساب الذهب والأحجار")
    st.markdown("**الحساب بالدولار ($)**")
    w_col1, w_col2, w_col3 = st.columns([2, 2, 1])
    with w_col1:
        gold_weight = st.number_input("وزن الذهب (جرام)", min_value=0.0, format="%.2f")
    with w_col2:
        workmanship_price_usd = st.number_input("سعر مصنعية الجرام ($)", min_value=0.0, format="%.2f")
    workmanship_subtotal_usd = gold_weight * workmanship_price_usd
    with w_col3:
        st.metric(label="ناتج المصنعية", value=f"$ {workmanship_subtotal_usd:.2f}")

    s_col1, s_col2, s_col3 = st.columns([2, 2, 1])
    with s_col1:
        stone_weight_carats = st.number_input("وزن أحجار الورشة (قيراط)", min_value=0.0, format="%.3f")
    with s_col2:
        stone_price_per_carat_usd = st.number_input("سعر القيراط ($)", min_value=0.0, format="%.2f")
    stone_cost_usd = stone_weight_carats * stone_price_per_carat_usd
    with s_col3:
        st.metric(label="ناتج سعر الأحجار", value=f"$ {stone_cost_usd:.2f}")
    
    st.markdown("---")
    st.markdown("**الحساب بالجنيه المصري (EGP)**")
    
    e_col1, e_col2, e_col3 = st.columns([2, 2, 1])
    with e_col1:
        stone_count = st.number_input("عدد أحجار العميل", min_value=0, step=1)
    with e_col2:
        stone_setting_price_egp = st.number_input("سعر تركيب الحجر (EGP)", min_value=0.0, format="%.2f")
    stone_setting_cost_egp = stone_count * stone_setting_price_egp
    with e_col3:
        st.metric(label="ناتج التركيب", value=f"{stone_setting_cost_egp:.2f} ج.م")

with st.container(border=True):
    st.subheader("🔧 خدمات إضافية وتصليح (EGP)")
    serv_col1, serv_col2, serv_col3, serv_col4 = st.columns(4)
    with serv_col1:
        plating_white_egp = st.number_input("بانيو أبيض", min_value=0.0, format="%.2f")
    with serv_col2:
        plating_yellow_egp = st.number_input("بانيو أصفر", min_value=0.0, format="%.2f")
    with serv_col3:
        hallmark_egp = st.number_input("دمغة", min_value=0.0, format="%.2f")
    with serv_col4:
        repair_egp = st.number_input("تصليح", min_value=0.0, format="%.2f")

with st.container(border=True):
    st.subheader("📝 مواصفات الأحجار (اختياري)")
    spec_col1, spec_col2, spec_col3 = st.columns(3)
    with spec_col1:
        st.text_input("مقاس الحجر", key="size")
    with spec_col2:
        st.text_input("نوع الحجر", key="type")
    with spec_col3:
        st.text_input("اللون والنقاء (Quality)", key="quality")


# --- الملخص النهائي ---
st.write("---")
with st.container(border=True):
    st.header("💵 الملخص النهائي للحساب")
    
    final_usd_charge = workmanship_subtotal_usd + stone_cost_usd
    final_egp_charge = stone_setting_cost_egp + plating_white_egp + plating_yellow_egp + hallmark_egp + repair_egp

    round_usd_checkbox = st.checkbox("تقريب المبلغ بالدولار لأعلى رقم صحيح؟")
    if round_usd_checkbox:
        final_usd_charge = math.ceil(final_usd_charge)

    res_col1, res_col2, res_col3 = st.columns(3)
    with res_col1:
        st.metric("التغير في رصيد الذهب", f"{-gold_weight:.2f} جرام")
    with res_col2:
        st.metric("إجمالي المطلوب بالدولار", f"$ {final_usd_charge:.2f}")
    with res_col3:
        st.metric("إجمالي المطلوب بالجنيه", f"{final_egp_charge:.2f} جنيه")
    
    # --- تعديل زر الحفظ ---
    if st.button("حفظ الفاتورة"):
        # استدعاء دالة الحفظ
        save_invoice_to_csv(
            customer=customer_name,
            date=str(invoice_date),
            gold=-gold_weight,
            usd=final_usd_charge,
            egp=final_egp_charge,
            desc=invoice_description
        )
        st.success(f"تم حفظ الفاتورة بنجاح للعميل: {customer_name}")