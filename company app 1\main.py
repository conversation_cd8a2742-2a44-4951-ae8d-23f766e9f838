"""
التطبيق الرئيسي - Crestal Diamond Management System
نقطة الدخول الرئيسية للنظام
"""

import sys
import os
from pathlib import Path

# إضافة مسار المشروع إلى Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import streamlit as st
from config.settings import STREAMLIT_CONFIG, APP_TITLE, APP_VERSION
from src.core.database import DatabaseManager

# إعداد الصفحة
st.set_page_config(**STREAMLIT_CONFIG)

# الشريط الجانبي
st.sidebar.title(f"💎 {APP_TITLE}")
st.sidebar.markdown(f"**الإصدار:** {APP_VERSION}")
st.sidebar.markdown("---")

# قائمة التنقل
pages = [
    "📄 إنشاء فاتورة جديدة",
    "📊 عرض الفواتير المحفوظة", 
    "👥 حسابات العملاء",
    "🔍 تحليل ملفات Excel",
    "📈 إحصائيات وتقارير",
    "⚙️ الإعدادات"
]

selected_page = st.sidebar.selectbox("اختر الصفحة:", pages)

# تهيئة قاعدة البيانات
@st.cache_resource
def init_database():
    return DatabaseManager()

db = init_database()

# تحميل الصفحات
if selected_page == "📄 إنشاء فاتورة جديدة":
    from src.pages.invoice_creation import show_invoice_creation_page
    show_invoice_creation_page(db)

elif selected_page == "📊 عرض الفواتير المحفوظة":
    from src.pages.invoice_list import show_invoice_list_page
    show_invoice_list_page(db)

elif selected_page == "👥 حسابات العملاء":
    from src.pages.customer_accounts import show_customer_accounts_page
    show_customer_accounts_page(db)

elif selected_page == "🔍 تحليل ملفات Excel":
    from src.pages.excel_analysis import show_excel_analysis_page
    show_excel_analysis_page()

elif selected_page == "📈 إحصائيات وتقارير":
    from src.pages.reports import show_reports_page
    show_reports_page(db)

elif selected_page == "⚙️ الإعدادات":
    from src.pages.settings import show_settings_page
    show_settings_page(db)

# تذييل الشريط الجانبي
st.sidebar.markdown("---")
st.sidebar.markdown("💎 **Crestal Diamond**")
st.sidebar.markdown("🔧 نظام إدارة الورشة")
st.sidebar.markdown(f"📅 {st.session_state.get('current_date', '2024')}")

# معلومات إضافية
with st.sidebar.expander("ℹ️ معلومات النظام"):
    st.write("""
    **المميزات:**
    - إدارة الفواتير
    - حسابات العملاء
    - تحليل ملفات Excel
    - التقارير والإحصائيات
    - النسخ الاحتياطية
    """)

# رسالة ترحيب في الصفحة الرئيسية
if 'welcome_shown' not in st.session_state:
    st.success("🎉 مرحباً بك في نظام إدارة ورشة Crestal Diamond!")
    st.session_state.welcome_shown = True
