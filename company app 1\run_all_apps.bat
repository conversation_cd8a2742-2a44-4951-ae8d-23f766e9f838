@echo off
echo ========================================
echo تشغيل جميع تطبيقات Crestal Diamond
echo ========================================

echo.
echo جاري تشغيل التطبيقات...

echo.
echo 1. التطبيق الرئيسي المحسن (المنفذ 8504)
start "التطبيق الرئيسي" cmd /k "streamlit run invoice_app.py --server.port 8504"

timeout /t 3 /nobreak >nul

echo 2. صفحة حسابات العملاء (المنفذ 8505)
start "حسابات العملاء" cmd /k "streamlit run pages/01_بيانات_العملاء.py --server.port 8505"

timeout /t 3 /nobreak >nul

echo 3. محلل ملفات Excel (المنفذ 8503)
start "محلل Excel" cmd /k "streamlit run excel_analyzer.py --server.port 8503"

echo.
echo ========================================
echo تم تشغيل جميع التطبيقات بنجاح!
echo ========================================

echo.
echo الروابط:
echo 1. التطبيق الرئيسي: http://localhost:8504
echo 2. حسابات العملاء: http://localhost:8505
echo 3. محلل Excel: http://localhost:8503

echo.
echo اضغط أي مفتاح للخروج...
pause >nul
